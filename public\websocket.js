// WebSocket客户端管理类
class WebSocketClient{
  constructor(){
    this.ws=null;
    this.status=CONFIG.status.disconnected;
    this.clientId=null;
    this.connectTime=null;
    this.heartbeatTimer=null;
    this.callbacks={
      onStatusChange:null,
      onMessage:null,
      onConnect:null,
      onDisconnect:null,
      onError:null
    };
  }

  // 连接WebSocket服务器
  connect(url){
    if(this.ws&&this.ws.readyState===WebSocket.OPEN){
      this.addMessage('已经连接到服务器','system');
      return;
    }

    this.setStatus(CONFIG.status.connecting);

    try{
      this.ws=new WebSocket(url);
      this.setupEventHandlers();
      this.addMessage(`正在连接到 ${url}...`,'system');
    }catch(error){
      this.handleError(`连接失败: ${error.message}`);
    }
  }

  // 设置事件处理器
  setupEventHandlers(){
    this.ws.onopen=()=>{
      this.setStatus(CONFIG.status.connected);
      this.connectTime=new Date();
      this.startHeartbeat();
      this.addMessage('连接成功','system');
      this.callbacks.onConnect?.();
    };

    this.ws.onmessage=(event)=>{
      try{
        const data=JSON.parse(event.data);
        this.handleMessage(data);
      }catch{
        this.addMessage(`收到原始数据: ${event.data}`,'received');
      }
    };

    this.ws.onclose=()=>{
      this.setStatus(CONFIG.status.disconnected);
      this.stopHeartbeat();
      this.addMessage('连接已断开','system');
      this.callbacks.onDisconnect?.();
    };

    this.ws.onerror=(error)=>{
      this.handleError(`WebSocket错误: ${error.message||'未知错误'}`);
    };
  }

  // 处理接收到的消息
  handleMessage(data){
    switch(data.type){
      case CONFIG.messageTypes.connect:
        this.clientId=data.clientId;
        this.addMessage(data.message,'system');
        break;
      case CONFIG.messageTypes.data:
        this.addMessage(`[客户端${data.sender}] ${data.data}`,'received');
        break;
      case CONFIG.messageTypes.heartbeat:
        // 静默处理心跳
        break;
      case CONFIG.messageTypes.error:
        this.addMessage(`服务器错误: ${data.message}`,'error');
        break;
      default:
        this.addMessage(`未知消息类型: ${JSON.stringify(data)}`,'received');
    }
    this.callbacks.onMessage?.(data);
  }

  // 发送消息
  send(message){
    if(!this.isConnected()){
      this.addMessage('未连接到服务器，无法发送消息','error');
      return false;
    }

    try{
      const data={
        type:CONFIG.messageTypes.data,
        data:message,
        timestamp:Date.now()
      };
      this.ws.send(JSON.stringify(data));
      this.addMessage(`发送: ${message}`,'sent');
      return true;
    }catch(error){
      this.handleError(`发送失败: ${error.message}`);
      return false;
    }
  }

  // 断开连接
  disconnect(){
    if(this.ws){
      this.ws.close();
      this.ws=null;
    }
    this.stopHeartbeat();
    this.setStatus(CONFIG.status.disconnected);
    this.clientId=null;
    this.connectTime=null;
  }

  // 检查连接状态
  isConnected(){
    return this.ws&&this.ws.readyState===WebSocket.OPEN;
  }

  // 设置状态
  setStatus(status){
    this.status=status;
    this.callbacks.onStatusChange?.(status);
  }

  // 开始心跳
  startHeartbeat(){
    this.stopHeartbeat();
    this.heartbeatTimer=setInterval(()=>{
      if(this.isConnected()){
        this.ws.send(JSON.stringify({
          type:CONFIG.messageTypes.heartbeat,
          timestamp:Date.now()
        }));
      }
    },CONFIG.heartbeatInterval);
  }

  // 停止心跳
  stopHeartbeat(){
    if(this.heartbeatTimer){
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer=null;
    }
  }

  // 处理错误
  handleError(message){
    this.setStatus(CONFIG.status.error);
    this.addMessage(message,'error');
    this.callbacks.onError?.(message);
  }

  // 添加消息到终端
  addMessage(text,type='system'){
    const terminal=document.getElementById('terminalOutput');

    // 如果存在欢迎界面，先移除
    const welcomeElement = terminal.querySelector('.terminal-welcome');
    if (welcomeElement) {
      welcomeElement.style.opacity = '0';
      setTimeout(() => {
        if (welcomeElement.parentNode) {
          welcomeElement.remove();
        }
      }, 300);
    }

    const message=document.createElement('div');
    message.className=`message ${type}`;

    const timestamp=new Date().toLocaleTimeString();
    message.innerHTML=`<span class="timestamp">[${timestamp}]</span>${text}`;

    // 添加入场动画
    message.style.opacity = '0';
    message.style.transform = 'translateY(20px)';
    terminal.appendChild(message);

    // 触发动画
    requestAnimationFrame(() => {
      message.style.transition = 'all 0.4s cubic-bezier(0.25, 1, 0.5, 1)';
      message.style.opacity = '1';
      message.style.transform = 'translateY(0)';
    });

    // 自动滚动到底部
    if(CONFIG.ui.autoScroll){
      setTimeout(() => {
        terminal.scrollTop = terminal.scrollHeight;
      }, 100);
    }

    // 限制消息数量
    while(terminal.children.length>CONFIG.ui.maxMessages){
      const firstChild = terminal.firstChild;
      if (firstChild && !firstChild.classList?.contains('terminal-welcome')) {
        terminal.removeChild(firstChild);
      } else {
        break;
      }
    }
  }

  // 设置回调函数
  on(event,callback){
    const eventName=`on${event.charAt(0).toUpperCase()+event.slice(1)}`;
    if(this.callbacks.hasOwnProperty(eventName)){
      this.callbacks[eventName]=callback;
    }
  }
}
