// 主应用逻辑
class TerminalApp{
  constructor(){
    this.wsClient=new WebSocketClient();
    this.elements={};
    this.init();
  }

  // 初始化应用
  init(){
    this.initElements();
    this.bindEvents();
    this.setupWebSocketCallbacks();
    this.updateUI();
    this.showWelcomeMessage();
  }

  // 初始化DOM元素
  initElements(){
    this.elements={
      statusIndicator:document.getElementById('statusIndicator'),
      serverUrl:document.getElementById('serverUrl'),
      connectBtn:document.getElementById('connectBtn'),
      disconnectBtn:document.getElementById('disconnectBtn'),
      terminalOutput:document.getElementById('terminalOutput'),
      messageInput:document.getElementById('messageInput'),
      sendBtn:document.getElementById('sendBtn'),
      clearBtn:document.getElementById('clearBtn'),
      scrollBtn:document.getElementById('scrollBtn'),
      connectionStatus:document.getElementById('connectionStatus'),
      clientId:document.getElementById('clientId'),
      connectTime:document.getElementById('connectTime'),
      onlineClients:document.getElementById('onlineClients')
    };

    // 调试：检查元素是否正确获取
    console.log('DOM元素初始化:', this.elements);

    // 设置默认值
    if(this.elements.serverUrl) {
      this.elements.serverUrl.value=CONFIG.defaultUrl;
    }
  }

  // 绑定事件
  bindEvents(){
    // 连接按钮
    this.elements.connectBtn.addEventListener('click',()=>{
      const url=this.elements.serverUrl.value.trim()||CONFIG.defaultUrl;
      console.log('尝试连接到:', url);
      this.wsClient.connect(url);
    });

    // 断开按钮
    this.elements.disconnectBtn.addEventListener('click',()=>{
      this.wsClient.disconnect();
    });

    // 发送按钮
    this.elements.sendBtn.addEventListener('click',()=>{
      this.sendMessage();
    });

    // 输入框回车发送
    this.elements.messageInput.addEventListener('keypress',(e)=>{
      if(e.key==='Enter'&&!e.shiftKey){
        e.preventDefault();
        this.sendMessage();
      }
    });

    // 清空终端
    this.elements.clearBtn.addEventListener('click',()=>{
      this.elements.terminalOutput.innerHTML='';
      this.wsClient.addMessage('终端已清空','system');
    });

    // 滚动到底部
    this.elements.scrollBtn.addEventListener('click',()=>{
      this.elements.terminalOutput.scrollTop=this.elements.terminalOutput.scrollHeight;
    });

    // 输入框自动聚焦
    this.elements.messageInput.addEventListener('focus',()=>{
      this.elements.messageInput.select();
    });

    // 服务器URL输入框回车连接
    this.elements.serverUrl.addEventListener('keypress',(e)=>{
      if(e.key==='Enter'){
        this.elements.connectBtn.click();
      }
    });
  }

  // 设置WebSocket回调
  setupWebSocketCallbacks(){
    this.wsClient.on('statusChange',(status)=>{
      this.updateStatus(status);
      this.updateUI();
    });

    this.wsClient.on('connect',()=>{
      this.updateConnectionInfo();
    });

    this.wsClient.on('disconnect',()=>{
      this.updateConnectionInfo();
    });

    this.wsClient.on('error',(error)=>{
      console.error('WebSocket错误:',error);
    });

    this.wsClient.on('message',(data)=>{
      // 可以在这里处理特殊消息类型
      console.log('收到消息:',data);
    });
  }

  // 发送消息
  sendMessage(){
    const message=this.elements.messageInput.value.trim();
    if(!message){
      this.wsClient.addMessage('请输入消息内容','error');
      return;
    }

    if(this.wsClient.send(message)){
      this.elements.messageInput.value='';
      this.elements.messageInput.focus();
    }
  }

  // 更新状态显示
  updateStatus(status){
    const statusTexts={
      [CONFIG.status.disconnected]:'未连接',
      [CONFIG.status.connecting]:'连接中...',
      [CONFIG.status.connected]:'已连接',
      [CONFIG.status.error]:'连接错误'
    };

    const statusText=statusTexts[status]||'未知状态';

    // 更新导航栏状态徽章
    const statusBadge = this.elements.statusIndicator;
    const statusTextElement = statusBadge.querySelector('.status-text');
    if (statusTextElement) {
      statusTextElement.textContent = statusText;
    }
    statusBadge.className = `status-badge ${status}`;

    // 更新状态卡片中的连接状态
    this.elements.connectionStatus.textContent=statusText;
  }

  // 更新UI状态
  updateUI(){
    const isConnected=this.wsClient.isConnected();
    const isConnecting=this.wsClient.status===CONFIG.status.connecting;

    // 连接控制
    this.elements.connectBtn.disabled=isConnected||isConnecting;
    this.elements.disconnectBtn.disabled=!isConnected;
    this.elements.serverUrl.disabled=isConnected||isConnecting;

    // 消息发送
    this.elements.messageInput.disabled=!isConnected;
    this.elements.sendBtn.disabled=!isConnected;

    // 按钮文本 - 更新span元素内的文本
    const connectBtnText = this.elements.connectBtn.querySelector('span');
    if (connectBtnText) {
      connectBtnText.textContent = isConnecting ? '连接中...' : '连接服务器';
    }

    // 如果连接成功，自动聚焦到消息输入框
    if(isConnected){
      setTimeout(()=>{
        this.elements.messageInput.focus();
      },100);
    }
  }

  // 更新连接信息
  updateConnectionInfo(){
    this.elements.clientId.textContent=this.wsClient.clientId||'-';
    this.elements.connectTime.textContent=this.wsClient.connectTime?
      this.wsClient.connectTime.toLocaleString():'-';
  }

  // 显示欢迎消息
  showWelcomeMessage(){
    // 清空欢迎界面，准备显示消息
    const terminalOutput = document.getElementById('terminalOutput');
    const welcomeElement = terminalOutput.querySelector('.terminal-welcome');
    if (welcomeElement) {
      welcomeElement.style.opacity = '0';
      setTimeout(() => {
        welcomeElement.remove();
      }, 300);
    }

    setTimeout(()=>{
      this.wsClient.addMessage('=== 智能终端管理系统 ===','system');
      this.wsClient.addMessage('欢迎使用智能终端管理系统！','system');
      this.wsClient.addMessage('请输入服务器地址，然后点击连接按钮','system');
      this.wsClient.addMessage('连接成功后，您可以发送消息与其他客户端通信','system');
      this.wsClient.addMessage('系统会自动转发您的消息给所有其他连接的客户端','system');
      this.wsClient.addMessage('================================','system');
    },800);
  }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded',()=>{
  window.terminalApp=new TerminalApp();
  
  // 添加全局错误处理
  window.addEventListener('error',(event)=>{
    console.error('全局错误:',event.error);
    if(window.terminalApp&&window.terminalApp.wsClient){
      window.terminalApp.wsClient.addMessage(`系统错误: ${event.error.message}`,'error');
    }
  });
});

// 页面卸载时断开连接
window.addEventListener('beforeunload',()=>{
  if(window.terminalApp&&window.terminalApp.wsClient){
    window.terminalApp.wsClient.disconnect();
  }
});
