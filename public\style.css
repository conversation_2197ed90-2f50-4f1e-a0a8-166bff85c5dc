/* 苹果风格现代化设计 */
:root {
  /* 颜色系统 */
  --primary-blue: #007AFF;
  --primary-blue-hover: #0056CC;
  --secondary-gray: #8E8E93;
  --background-primary: #F2F2F7;
  --background-secondary: #FFFFFF;
  --background-tertiary: #F8F9FA;
  --surface-elevated: rgba(255, 255, 255, 0.8);
  --text-primary: #1D1D1F;
  --text-secondary: #86868B;
  --text-tertiary: #6D6D70;
  --border-light: rgba(0, 0, 0, 0.1);
  --border-medium: rgba(0, 0, 0, 0.15);
  --success: #30D158;
  --warning: #FF9F0A;
  --error: #FF3B30;
  --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8px 25px rgba(0, 0, 0, 0.15);
  --blur-background: blur(20px);

  /* 动画曲线 */
  --ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
  --ease-in-out-quart: cubic-bezier(0.76, 0, 0.24, 1);
  --spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 全局重置与基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--background-primary);
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
}

/* 应用容器 */
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 导航栏 */
.navbar {
  background: var(--surface-elevated);
  backdrop-filter: var(--blur-background);
  -webkit-backdrop-filter: var(--blur-background);
  border-bottom: 1px solid var(--border-light);
  position: sticky;
  top: 0;
  z-index: 100;
  transition: all 0.3s var(--ease-out-quart);
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 12px;
}

.brand-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--primary-blue), #5856D6);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: transform 0.2s var(--ease-out-quart);
}

.brand-icon:hover {
  transform: scale(1.05);
}

.brand-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: -0.02em;
}

/* 状态徽章 */
.status-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: var(--background-secondary);
  border: 1px solid var(--border-light);
  border-radius: 20px;
  transition: all 0.3s var(--ease-out-quart);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--secondary-gray);
  transition: all 0.3s var(--ease-out-quart);
}

.status-badge.connected .status-dot {
  background: var(--success);
  box-shadow: 0 0 8px rgba(48, 209, 88, 0.4);
}

.status-badge.connecting .status-dot {
  background: var(--warning);
  animation: pulse 2s infinite;
}

.status-badge.error .status-dot {
  background: var(--error);
  animation: pulse 1s infinite;
}

.status-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 32px 24px;
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
}

/* 卡片组件 */
.card {
  background: var(--background-secondary);
  border: 1px solid var(--border-light);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: var(--shadow-light);
  transition: all 0.3s var(--ease-out-quart);
}

.card:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}

.card-header {
  padding: 24px 24px 16px;
  border-bottom: 1px solid var(--border-light);
}

.card-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.card-title svg {
  color: var(--primary-blue);
}

.card-subtitle {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
}

.card-content {
  padding: 24px;
}

/* 输入字段 */
.input-field {
  margin-bottom: 24px;
}

.field-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.input-wrapper {
  position: relative;
}

.field-input {
  width: 100%;
  padding: 16px 48px 16px 16px;
  border: 1px solid var(--border-medium);
  border-radius: 12px;
  background: var(--background-tertiary);
  font-size: 16px;
  color: var(--text-primary);
  transition: all 0.3s var(--ease-out-quart);
}

.field-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  background: var(--background-secondary);
  box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
}

.field-input::placeholder {
  color: var(--text-tertiary);
}

.input-icon {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-tertiary);
  pointer-events: none;
}

/* 按钮组件 */
.button-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s var(--ease-out-quart);
  text-decoration: none;
  white-space: nowrap;
}

.btn-primary {
  background: var(--primary-blue);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-blue-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.btn-primary:active:not(:disabled) {
  transform: translateY(0);
}

.btn-primary:disabled {
  background: var(--secondary-gray);
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  background: var(--background-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-medium);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--background-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-light);
}

.btn-secondary:disabled {
  background: var(--background-tertiary);
  color: var(--text-tertiary);
  cursor: not-allowed;
  transform: none;
}

/* 终端控制按钮 */
.terminal-controls {
  display: flex;
  gap: 8px;
}

.control-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  background: var(--background-tertiary);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s var(--ease-out-quart);
}

.control-btn:hover {
  background: var(--background-primary);
  color: var(--text-primary);
  transform: scale(1.05);
}

/* 终端容器 */
.terminal-container {
  height: 500px;
  display: flex;
  flex-direction: column;
}

.terminal-output {
  flex: 1;
  padding: 24px;
  background: #1E1E1E;
  color: #D4D4D4;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 14px;
  line-height: 1.6;
  overflow-y: auto;
  border-radius: 0;
}

.terminal-output::-webkit-scrollbar {
  width: 8px;
}

.terminal-output::-webkit-scrollbar-track {
  background: transparent;
}

.terminal-output::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.terminal-output::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 终端欢迎界面 */
.terminal-welcome {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  opacity: 0.7;
}

.welcome-icon {
  margin-bottom: 16px;
  color: var(--primary-blue);
}

.terminal-welcome h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #D4D4D4;
}

.terminal-welcome p {
  font-size: 14px;
  color: #8E8E93;
}

/* 消息输入区域 */
.message-input-container {
  padding: 16px 24px;
  background: var(--background-secondary);
  border-top: 1px solid var(--border-light);
}

.message-input-container .input-wrapper {
  display: flex;
  gap: 12px;
  align-items: center;
}

.message-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid var(--border-medium);
  border-radius: 24px;
  background: var(--background-tertiary);
  font-size: 16px;
  color: var(--text-primary);
  transition: all 0.3s var(--ease-out-quart);
}

.message-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  background: var(--background-secondary);
  box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
}

.message-input::placeholder {
  color: var(--text-tertiary);
}

.message-input:disabled {
  background: var(--background-primary);
  color: var(--text-tertiary);
  cursor: not-allowed;
}

.send-btn {
  width: 44px;
  height: 44px;
  border: none;
  border-radius: 50%;
  background: var(--primary-blue);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s var(--ease-out-quart);
}

.send-btn:hover:not(:disabled) {
  background: var(--primary-blue-hover);
  transform: scale(1.05);
}

.send-btn:disabled {
  background: var(--secondary-gray);
  cursor: not-allowed;
  transform: none;
}

/* 消息样式 */
.message {
  margin-bottom: 16px;
  padding: 12px 16px;
  border-radius: 12px;
  word-wrap: break-word;
  animation: messageSlideIn 0.4s var(--ease-out-quart);
  position: relative;
}

.message.system {
  background: rgba(255, 159, 10, 0.1);
  border-left: 3px solid var(--warning);
  color: #D4D4D4;
}

.message.sent {
  background: rgba(48, 209, 88, 0.1);
  border-left: 3px solid var(--success);
  color: #D4D4D4;
}

.message.received {
  background: rgba(0, 122, 255, 0.1);
  border-left: 3px solid var(--primary-blue);
  color: #D4D4D4;
}

.message.error {
  background: rgba(255, 59, 48, 0.1);
  border-left: 3px solid var(--error);
  color: #D4D4D4;
}

.message .timestamp {
  font-size: 12px;
  color: #8E8E93;
  margin-right: 12px;
  font-weight: 500;
}

.message .sender {
  font-weight: 600;
  color: var(--primary-blue);
  margin-right: 8px;
}

/* 状态网格 */
.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: var(--background-tertiary);
  border-radius: 12px;
  transition: all 0.3s var(--ease-out-quart);
}

.status-item:hover {
  background: var(--background-secondary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-light);
}

.status-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--primary-blue), #5856D6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.status-content {
  flex: 1;
}

.status-label {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.status-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card {
  animation: cardSlideIn 0.6s var(--ease-out-quart);
}

.card:nth-child(1) { animation-delay: 0.1s; }
.card:nth-child(2) { animation-delay: 0.2s; }
.card:nth-child(3) { animation-delay: 0.3s; }

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-content {
    padding: 12px 16px;
  }

  .brand-title {
    font-size: 18px;
  }

  .main-content {
    padding: 20px 16px;
    gap: 20px;
  }

  .card-header {
    padding: 20px 20px 12px;
  }

  .card-content {
    padding: 20px;
  }

  .button-group {
    flex-direction: column;
  }

  .btn {
    justify-content: center;
  }

  .terminal-container {
    height: 400px;
  }

  .status-grid {
    grid-template-columns: 1fr;
  }

  .status-item {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .nav-brand {
    gap: 8px;
  }

  .brand-icon {
    width: 28px;
    height: 28px;
  }

  .brand-title {
    font-size: 16px;
  }

  .main-content {
    padding: 16px 12px;
  }

  .terminal-container {
    height: 350px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --background-primary: #000000;
    --background-secondary: #1C1C1E;
    --background-tertiary: #2C2C2E;
    --surface-elevated: rgba(28, 28, 30, 0.8);
    --text-primary: #FFFFFF;
    --text-secondary: #EBEBF5;
    --text-tertiary: #EBEBF5;
    --border-light: rgba(255, 255, 255, 0.1);
    --border-medium: rgba(255, 255, 255, 0.15);
  }

  .terminal-output {
    background: #000000;
  }
}
