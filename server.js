const WebSocket=require('ws'),express=require('express'),config=require('./config');
const app=express(),clients=new Map();
let clientId=0;

// HTTP静态文件服务
app.use(express.static('public'));
app.listen(config.server.http,'127.0.0.1',()=>console.log(`HTTP服务器启动: http://127.0.0.1:${config.server.http}`));

// WebSocket服务器
const wss=new WebSocket.Server({
  port:config.server.port,
  host:config.server.host,
  maxClients:config.ws.maxClients
});

// 数据转发函数
const forwardData=(data,sender)=>{
  const message=JSON.stringify({
    type:config.msg.data,
    data:data,
    sender:sender?.id||'server',
    timestamp:Date.now()
  });
  
  clients.forEach((client,ws)=>{
    if(ws.readyState===WebSocket.OPEN&&ws!==sender?.ws){
      ws.send(message);
    }
  });
};

// 心跳检测
const heartbeat=()=>{
  const ping=JSON.stringify({
    type:config.msg.heartbeat,
    timestamp:Date.now()
  });
  
  const now=Date.now();
  clients.forEach((client,ws)=>{
    if(ws.readyState===WebSocket.OPEN){
      if(now-client.lastPing>config.ws.timeout){
        console.log(`客户端${client.id}超时断开`);
        ws.terminate();
        clients.delete(ws);
      }else{
        ws.send(ping);
      }
    }
  });
};

// 连接处理
wss.on('connection',(ws,req)=>{
  const client={
    id:++clientId,
    ws:ws,
    ip:req.socket.remoteAddress,
    connectTime:Date.now(),
    lastPing:Date.now()
  };
  
  clients.set(ws,client);
  console.log(`客户端${client.id}已连接(${client.ip}) 当前连接数:${clients.size}`);
  
  // 发送连接确认
  ws.send(JSON.stringify({
    type:config.msg.connect,
    clientId:client.id,
    message:'连接成功'
  }));
  
  // 消息处理
  ws.on('message',(data)=>{
    const clientInfo=clients.get(ws);
    if(!clientInfo)return;
    
    clientInfo.lastPing=Date.now();
    
    try{
      const message=JSON.parse(data);
      console.log(`收到客户端${clientInfo.id}消息:`,message);
      
      // 转发数据给其他客户端
      if(message.type!==config.msg.heartbeat){
        forwardData(message.data||data.toString(),clientInfo);
      }
    }catch(error){
      console.log(`收到客户端${clientInfo.id}原始数据:`,data.toString());
      forwardData(data.toString(),clientInfo);
    }
  });
  
  // 断开连接处理
  ws.on('close',()=>{
    const clientInfo=clients.get(ws);
    if(clientInfo){
      console.log(`客户端${clientInfo.id}已断开连接`);
      clients.delete(ws);
    }
  });
  
  // 错误处理
  ws.on('error',(error)=>{
    console.error(`WebSocket错误:`,error);
    const clientInfo=clients.get(ws);
    if(clientInfo){
      clients.delete(ws);
    }
  });
});

// 定时心跳检测
setInterval(heartbeat,config.ws.heartbeat);

console.log(`WebSocket服务器启动: ws://${config.server.host}:${config.server.port}`);
console.log(`前端访问地址: http://127.0.0.1:${config.server.http}`);

// 优雅关闭
process.on('SIGINT',()=>{
  console.log('\n正在关闭服务器...');
  wss.close(()=>{
    console.log('WebSocket服务器已关闭');
    process.exit(0);
  });
});
